<template>
  <div class="content-collect">
    <!-- 页面头部 -->
    <div class="page-header">
      <h1>📚 内容采集管理</h1>
      <p class="header-description">统一管理和查看所有平台采集到的内容，支持搜索、筛选和预览</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="stat-card total">
            <div class="stat-content">
              <div class="stat-icon">📊</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.total_content }}</div>
                <div class="stat-label">总内容数</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card accounts">
            <div class="stat-content">
              <div class="stat-icon">🎯</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.total_accounts }}</div>
                <div class="stat-label">采集账号</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card downloadable">
            <div class="stat-content">
              <div class="stat-icon">📥</div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.downloadable_content }}</div>
                <div class="stat-label">可下载</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card size">
            <div class="stat-content">
              <div class="stat-icon">💾</div>
              <div class="stat-info">
                <div class="stat-number">{{ formatFileSize(stats.total_size) }}</div>
                <div class="stat-label">总大小</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 筛选工具栏 -->
    <div class="filter-toolbar">
      <div class="filter-left">
        <el-input
          v-model="searchQuery"
          placeholder="搜索内容标题..."
          style="width: 300px"
          @input="handleSearch"
          clearable
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>

        <el-select
          v-model="filterPlatform"
          placeholder="平台筛选"
          style="width: 120px; margin-left: 10px"
          @change="loadContent"
          clearable
        >
          <el-option label="全部平台" value="" />
          <el-option label="抖音" value="douyin" />
          <el-option label="YouTube" value="youtube" />
          <el-option label="TikTok" value="tiktok" />
          <el-option label="Instagram" value="instagram" />
        </el-select>

        <el-select
          v-model="filterAccount"
          placeholder="选择账号"
          style="width: 200px; margin-left: 10px"
          @change="loadContent"
          clearable
          filterable
        >
          <el-option label="全部账号" value="" />
          <el-option
            v-for="account in availableAccounts"
            :key="account.id"
            :label="account.name"
            :value="account.id"
          />
        </el-select>

        <el-select
          v-model="filterStatus"
          placeholder="状态筛选"
          style="width: 120px; margin-left: 10px"
          @change="loadContent"
          clearable
        >
          <el-option label="全部状态" value="" />
          <el-option label="待下载" value="pending" />
          <el-option label="已下载" value="completed" />
          <el-option label="下载失败" value="failed" />
        </el-select>

        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="width: 240px; margin-left: 10px"
          @change="loadContent"
          clearable
        />
      </div>

      <div class="filter-right">
        <el-button @click="loadContent" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
        <el-button type="primary" @click="showBatchDownloadDialog = true" :disabled="selectedContent.length === 0">
          <el-icon><Download /></el-icon>
          批量下载 ({{ selectedContent.length }})
        </el-button>
        <el-button type="success" @click="exportContent">
          <el-icon><Upload /></el-icon>
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 内容列表 -->
    <div class="content-list">
      <el-table
        :data="contentList"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        height="600"
      >
        <template #empty>
          <div style="padding: 40px; text-align: center;">
            <el-icon size="48" style="color: #c0c4cc; margin-bottom: 16px;">
              <Collection />
            </el-icon>
            <p style="color: #909399; margin: 0;">暂无采集内容</p>
            <p style="color: #c0c4cc; font-size: 12px; margin: 8px 0 0 0;">
              请前往对标账号管理创建采集任务
            </p>
          </div>
        </template>

        <el-table-column type="selection" width="55" />
        
        <el-table-column label="内容信息" min-width="350">
          <template #default="{ row }">
            <div class="content-info" :class="{ 'long-video': isLongVideo(row) }">
              <div class="content-header">
                <img
                  :src="row.cover_url"
                  :alt="row.title"
                  class="content-cover"
                  @error="handleImageError"
                />
                <div class="content-details">
                  <div class="content-title">
                    {{ row.title || '无标题' }}
                    <el-tag
                      v-if="isLongVideo(row)"
                      type="warning"
                      size="small"
                      class="long-video-tag"
                    >
                      长视频
                    </el-tag>
                  </div>
                  <div class="content-meta">
                    <span class="content-id">ID: {{ row.content_id }}</span>
                    <span class="content-type">{{ getContentTypeText(row.content_type) }}</span>
                    <span v-if="row.duration && row.content_type === 'video'" class="video-duration">
                      🎬 {{ formatDuration(row.duration) }}
                    </span>
                  </div>
                  <div class="content-stats">
                    <span v-if="row.like_count" class="stat-item">
                      👍 {{ formatNumber(row.like_count) }}
                    </span>
                    <span v-if="row.view_count" class="stat-item">
                      👁️ {{ formatNumber(row.view_count) }}
                    </span>
                    <span v-if="row.file_size" class="stat-item">
                      💾 {{ formatFileSize(row.file_size) }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="来源账号" width="200">
          <template #default="{ row }">
            <div class="source-account">
              <div class="account-name">{{ row.account_name }}</div>
              <div class="account-platform">
                <el-tag size="small" :type="getPlatformColor(row.platform)">
                  {{ getPlatformText(row.platform) }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="采集时间" width="120">
          <template #default="{ row }">
            <div class="collect-time">
              <div>{{ formatDate(row.created_at) }}</div>
              <div class="time-detail">{{ formatTime(row.created_at) }}</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="发布时间" width="120">
          <template #default="{ row }">
            <div class="publish-time">
              <div v-if="row.year && row.month">{{ row.year }}年{{ row.month }}月</div>
              <div v-else class="unknown-time">未知</div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="下载状态" width="120">
          <template #default="{ row }">
            <el-tag :type="getDownloadStatusColor(row.download_status)">
              {{ getDownloadStatusText(row.download_status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <div class="action-buttons">
              <el-button 
                size="small" 
                type="primary" 
                link
                @click="previewContent(row)"
              >
                预览
              </el-button>
              <el-button 
                size="small" 
                type="success" 
                link
                @click="downloadContent(row)"
                :disabled="!row.real_content_url || row.download_status === 'downloading'"
              >
                下载
              </el-button>
              <el-dropdown @command="(command) => handleActionCommand(command, row)">
                <el-button size="small" type="info" link>
                  更多 <el-icon><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">编辑信息</el-dropdown-item>
                    <el-dropdown-item command="copy-url">复制链接</el-dropdown-item>
                    <el-dropdown-item divided command="delete" style="color: #f56c6c;">删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="totalCount"
        :page-sizes="[20, 50, 100, 200]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="loadContent"
        @size-change="handleSizeChange"
      />
    </div>

    <!-- 批量下载对话框 -->
    <BatchDownloadDialog
      v-model="showBatchDownloadDialog"
      :selected-content="selectedContent"
      @success="handleBatchDownloadSuccess"
    />

    <!-- 内容预览对话框 -->
    <ContentPreviewDialog
      v-model="showPreviewDialog"
      :content="selectedContentItem"
    />

    <!-- 内容编辑对话框 -->
    <ContentEditDialog
      v-model="showEditDialog"
      :content="selectedContentItem"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, Refresh, Download, Upload, Collection, ArrowDown 
} from '@element-plus/icons-vue'
import { 
  getVideosByAccount, 
  getVideoStatistics, 
  downloadSingleVideo,
  deleteVideo as deleteVideoAPI,
  exportVideoList
} from '@/api/video-collect'

// 导入对话框组件
import BatchDownloadDialog from './components/BatchDownloadDialog.vue'
import ContentPreviewDialog from './components/ContentPreviewDialog.vue'
import ContentEditDialog from './components/ContentEditDialog.vue'

// 响应式数据
const loading = ref(false)
const contentList = ref<any[]>([])
const selectedContent = ref<any[]>([])
const selectedContentItem = ref<any>(null)

// 统计数据
const stats = reactive({
  total_content: 0,
  total_accounts: 0,
  downloadable_content: 0,
  total_size: 0
})

// 筛选条件
const searchQuery = ref('')
const filterPlatform = ref('')
const filterAccount = ref('')
const filterStatus = ref('')
const dateRange = ref<[Date, Date] | null>(null)

// 可用账号列表
const availableAccounts = ref<any[]>([])

// 分页
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

// 对话框状态
const showBatchDownloadDialog = ref(false)
const showPreviewDialog = ref(false)
const showEditDialog = ref(false)

// 方法
const loadContent = async () => {
  loading.value = true
  try {
    // TODO: 调用API获取内容列表
    // 这里需要一个新的API来获取所有平台的采集内容
    console.log('加载内容列表...')
    
    // 模拟数据
    const mockContent = Array.from({ length: pageSize.value }, (_, i) => {
      const contentType = ['video', 'image', 'audio'][Math.floor(Math.random() * 3)]
      const duration = contentType === 'video' ? Math.floor(Math.random() * 300) + 10 : null // 10-310秒的视频时长

      return {
        content_id: `content_${i + 1}`,
        title: `测试内容标题 ${i + 1}`,
        content_type: contentType,
        duration: duration,
        cover_url: `https://picsum.photos/120/68?random=${i}`,
        content_url: `https://example.com/content/${i + 1}`,
        real_content_url: i % 3 === 0 ? `https://example.com/download/${i + 1}.mp4` : '',
        like_count: Math.floor(Math.random() * 10000),
        view_count: Math.floor(Math.random() * 100000),
        file_size: Math.floor(Math.random() * ********),
        platform: ['douyin', 'youtube', 'tiktok'][Math.floor(Math.random() * 3)],
        account_name: `测试账号${i + 1}`,
        account_id: `account_${i + 1}`,
        year: '2024',
        month: String(Math.floor(Math.random() * 12) + 1).padStart(2, '0'),
        download_status: ['pending', 'downloading', 'completed', 'failed'][Math.floor(Math.random() * 4)],
        created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString()
      }
    })

    contentList.value = mockContent
    totalCount.value = 500 // 模拟总数

  } catch (error) {
    console.error('加载内容列表失败:', error)
    ElMessage.error('加载内容列表失败')
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  try {
    // TODO: 调用API获取统计数据
    Object.assign(stats, {
      total_content: 1250,
      total_accounts: 25,
      downloadable_content: 980,
      total_size: 1********00 // 15GB
    })
  } catch (error) {
    console.error('加载统计数据失败:', error)
  }
}

const loadAvailableAccounts = async () => {
  try {
    // TODO: 调用API获取可用账号列表
    availableAccounts.value = [
      { id: 'account_1', name: '测试账号1' },
      { id: 'account_2', name: '测试账号2' },
      { id: 'account_3', name: '测试账号3' },
    ]
  } catch (error) {
    console.error('加载账号列表失败:', error)
  }
}

// 工具方法
const formatDate = (dateString: string) => {
  if (!dateString) return '未知'
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const formatTime = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  }
  return num.toString()
}

const formatFileSize = (bytes: number) => {
  if (!bytes) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getContentTypeText = (type: string) => {
  const typeMap = {
    'video': '视频',
    'image': '图片',
    'audio': '音频',
    'text': '文本'
  }
  return typeMap[type as keyof typeof typeMap] || type
}

const getPlatformText = (platform: string) => {
  const platformMap = {
    'douyin': '抖音',
    'youtube': 'YouTube',
    'tiktok': 'TikTok',
    'instagram': 'Instagram'
  }
  return platformMap[platform as keyof typeof platformMap] || platform
}

const getPlatformColor = (platform: string) => {
  const colorMap = {
    'douyin': 'danger',
    'youtube': 'warning',
    'tiktok': 'info',
    'instagram': 'success'
  }
  return colorMap[platform as keyof typeof colorMap] || 'info'
}

const getDownloadStatusColor = (status: string) => {
  const colorMap = {
    'pending': 'info',
    'downloading': 'warning',
    'completed': 'success',
    'failed': 'danger'
  }
  return colorMap[status as keyof typeof colorMap] || 'info'
}

const getDownloadStatusText = (status: string) => {
  const textMap = {
    'pending': '待下载',
    'downloading': '下载中',
    'completed': '已完成',
    'failed': '失败'
  }
  return textMap[status as keyof typeof textMap] || '未知'
}

// 格式化视频时长
const formatDuration = (seconds: number) => {
  if (!seconds) return '未知'
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`
  }
}

// 判断是否为长视频（超过35秒）
const isLongVideo = (content: any) => {
  return content.content_type === 'video' && content.duration && content.duration > 35
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIwIiBoZWlnaHQ9IjY4IiB2aWV3Qm94PSIwIDAgMTIwIDY4IiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMTIwIiBoZWlnaHQ9IjY4IiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik02MCAzNEw3MCA0NEg1MEw2MCAzNFoiIGZpbGw9IiNDQ0NDQ0MiLz4KPC9zdmc+'
}

// 事件处理
const handleSearch = () => {
  setTimeout(() => {
    currentPage.value = 1
    loadContent()
  }, 500)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadContent()
}

const handleSelectionChange = (selection: any[]) => {
  selectedContent.value = selection
}

const previewContent = (content: any) => {
  selectedContentItem.value = content
  showPreviewDialog.value = true
}

const downloadContent = async (content: any) => {
  try {
    // TODO: 调用下载API
    ElMessage.success(`开始下载: ${content.title}`)
  } catch (error) {
    ElMessage.error('下载失败')
  }
}

const handleActionCommand = async (command: string, content: any) => {
  selectedContentItem.value = content
  
  switch (command) {
    case 'edit':
      showEditDialog.value = true
      break
    case 'copy-url':
      if (content.content_url) {
        await navigator.clipboard.writeText(content.content_url)
        ElMessage.success('链接已复制到剪贴板')
      }
      break
    case 'delete':
      try {
        await ElMessageBox.confirm(
          `确定要删除内容 "${content.title}" 吗？`,
          '确认删除',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }
        )
        // TODO: 调用删除API
        ElMessage.success('内容删除成功')
        loadContent()
      } catch (error) {
        // 用户取消删除
      }
      break
  }
}

const exportContent = async () => {
  try {
    // TODO: 调用导出API
    ElMessage.success('导出功能开发中...')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

const handleBatchDownloadSuccess = () => {
  ElMessage.success('批量下载任务已创建')
  loadContent()
}

const handleEditSuccess = () => {
  ElMessage.success('内容信息更新成功')
  loadContent()
}

// 生命周期
onMounted(() => {
  loadStats()
  loadAvailableAccounts()
  loadContent()
})
</script>

<style scoped>
.content-collect {
  padding: 20px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.header-description {
  color: #606266;
  font-size: 14px;
  margin: 0;
}

.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
}

.stat-icon {
  font-size: 32px;
  margin-right: 16px;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.filter-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.filter-left {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.filter-right {
  display: flex;
  gap: 8px;
}

.content-list {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.content-info {
  padding: 8px 0;
  transition: all 0.3s ease;
}

.content-info.long-video {
  background: linear-gradient(90deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
  border-left: 4px solid #ffc107;
  padding-left: 12px;
  margin-left: -8px;
  border-radius: 4px;
}

.content-header {
  display: flex;
  gap: 12px;
}

.content-cover {
  width: 120px;
  height: 68px;
  border-radius: 4px;
  object-fit: cover;
  flex-shrink: 0;
}

.content-details {
  flex: 1;
  min-width: 0;
}

.content-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.4;
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.long-video-tag {
  flex-shrink: 0;
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 3px;
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.content-meta {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
}

.content-id {
  margin-right: 12px;
}

.content-type {
  background: #f0f9ff;
  color: #0369a1;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
}

.video-duration {
  background: #f3e8ff;
  color: #7c3aed;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 500;
  margin-left: 8px;
}

.content-stats {
  font-size: 12px;
  color: #666;
}

.stat-item {
  margin-right: 12px;
}

.source-account {
  text-align: center;
}

.account-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.collect-time,
.publish-time {
  text-align: center;
  font-size: 12px;
}

.time-detail {
  color: #909399;
  margin-top: 2px;
}

.unknown-time {
  color: #c0c4cc;
  font-style: italic;
}

.action-buttons {
  display: flex;
  gap: 4px;
  align-items: center;
}

.pagination-container {
  margin-top: 16px;
  text-align: center;
}
</style>
